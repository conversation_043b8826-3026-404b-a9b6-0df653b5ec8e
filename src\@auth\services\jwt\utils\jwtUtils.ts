import jwtDecode, { JwtPayload } from 'jwt-decode';

export const isTokenValid = (accessToken: string) => {
	if (accessToken) {
		// En modo desarrollo, aceptar tokens mock
		if (import.meta.env.DEV && accessToken.startsWith('dev-token-')) {
			console.log('Token de desarrollo válido:', accessToken);
			return true;
		}

		try {
			const decoded = jwtDecode<JwtPayload>(accessToken);
			const currentTime = Date.now() / 1000;
			return decoded.exp > currentTime;
		} catch (error) {
			console.error('Error validando token JWT:', error);
			return false;
		}
	}

	return false;
};
