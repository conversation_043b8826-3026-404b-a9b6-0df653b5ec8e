#!/usr/bin/env node

/**
 * Script simple para probar la autenticación en el VPS usando curl
 * Ejecutar desde el VPS: node test-auth-simple.js
 */

const { exec } = require('child_process');
const util = require('util');
const execAsync = util.promisify(exec);

async function testVPSAuthentication() {
    console.log('🚀 Iniciando pruebas de autenticación en VPS...');
    
    try {
        // 1. Verificar que el servidor está corriendo
        console.log('📍 Verificando que el servidor está corriendo...');
        const { stdout: serverCheck } = await execAsync('curl -I http://localhost:3000');
        console.log('✅ Servidor respondiendo:', serverCheck.split('\n')[0]);
        
        // 2. Obtener la página de login
        console.log('📄 Obteniendo página de login...');
        const { stdout: loginPage } = await execAsync('curl -s http://localhost:3000/sign-in');
        
        if (loginPage.includes('Sign in') || loginPage.includes('sign-in')) {
            console.log('✅ Página de login cargada correctamente');
        } else {
            console.log('❌ Página de login no encontrada');
            return;
        }
        
        // 3. Verificar que la aplicación está en modo desarrollo
        console.log('🔧 Verificando configuración de desarrollo...');
        const { stdout: envCheck } = await execAsync('cat .env | grep VITE_DEV');
        console.log('📋 Configuración:', envCheck.trim());
        
        // 4. Verificar conectividad MySQL
        console.log('🗄️ Verificando conectividad MySQL...');
        const { stdout: mysqlCheck } = await execAsync('mysql -h ************** -u ncornejo -p"N1c0l7as17#" -e "SELECT COUNT(*) as user_count FROM renovatio.users;" 2>/dev/null || echo "Error de conexión MySQL"');
        
        if (mysqlCheck.includes('user_count')) {
            console.log('✅ MySQL conectado correctamente');
            console.log('📊 Resultado:', mysqlCheck.trim());
        } else {
            console.log('❌ Error de conexión MySQL:', mysqlCheck.trim());
        }
        
        // 5. Verificar archivos de configuración
        console.log('📁 Verificando archivos de configuración...');
        const { stdout: authFile } = await execAsync('ls -la src/@auth/authApi.ts');
        console.log('✅ Archivo de autenticación:', authFile.trim());
        
        // 6. Verificar que el puerto 3000 está abierto
        console.log('🌐 Verificando puerto 3000...');
        const { stdout: portCheck } = await execAsync('netstat -tlnp | grep :3000');
        console.log('✅ Puerto 3000:', portCheck.trim());
        
        // 7. Verificar firewall
        console.log('🔥 Verificando firewall...');
        const { stdout: firewallCheck } = await execAsync('ufw status | grep 3000');
        console.log('✅ Firewall:', firewallCheck.trim());
        
        console.log('\n🎉 Resumen de verificación:');
        console.log('✅ Servidor React: Funcionando');
        console.log('✅ Página de login: Accesible');
        console.log('✅ Configuración: Modo desarrollo');
        console.log('✅ Puerto 3000: Abierto');
        console.log('✅ Firewall: Configurado');
        
        console.log('\n📋 Para probar la autenticación manualmente:');
        console.log('1. Abre http://**************:3000/sign-in en tu navegador');
        console.log('2. Usa cualquier email y contraseña (ej: <EMAIL> / 123456)');
        console.log('3. Deberías ser redirigido al dashboard automáticamente');
        
    } catch (error) {
        console.error('❌ Error durante las pruebas:', error.message);
    }
}

// Ejecutar las pruebas
testVPSAuthentication()
    .then(() => {
        console.log('✅ Verificación completada');
        process.exit(0);
    })
    .catch((error) => {
        console.error('❌ Error fatal:', error);
        process.exit(1);
    });
