#!/usr/bin/env node

/**
 * Script para probar la autenticación en el VPS
 * Ejecutar desde el VPS: node test-vps-auth.js
 */

const { chromium } = require('playwright');

async function testVPSAuthentication() {
    console.log('🚀 Iniciando pruebas de autenticación en VPS...');
    
    const browser = await chromium.launch({ 
        headless: true,
        args: ['--no-sandbox', '--disable-setuid-sandbox']
    });
    
    const context = await browser.newContext();
    const page = await context.newPage();
    
    try {
        // 1. Navegar a la página de login
        console.log('📍 Navegando a http://localhost:3000/sign-in');
        await page.goto('http://localhost:3000/sign-in', { waitUntil: 'networkidle' });
        
        // 2. Verificar que la página cargó correctamente
        const title = await page.title();
        console.log(`📄 Título de la página: ${title}`);
        
        // 3. Verificar que los elementos de login están presentes
        const emailInput = await page.locator('input[name="email"]').first();
        const passwordInput = await page.locator('input[name="password"]').first();
        const signInButton = await page.locator('button[type="submit"]').first();
        
        console.log('🔍 Verificando elementos de login...');
        console.log(`✅ Campo email presente: ${await emailInput.isVisible()}`);
        console.log(`✅ Campo password presente: ${await passwordInput.isVisible()}`);
        console.log(`✅ Botón Sign In presente: ${await signInButton.isVisible()}`);
        
        // 4. Probar autenticación con credenciales de desarrollo
        console.log('🔐 Probando autenticación de desarrollo...');
        await emailInput.fill('<EMAIL>');
        await passwordInput.fill('cualquier_password');
        
        // Escuchar mensajes de consola para verificar modo desarrollo
        page.on('console', msg => {
            if (msg.text().includes('Modo desarrollo')) {
                console.log(`🔧 ${msg.text()}`);
            }
        });
        
        // 5. Hacer click en Sign In
        await signInButton.click();
        
        // 6. Esperar redirección o mensaje de error
        await page.waitForTimeout(3000);
        
        const currentUrl = page.url();
        console.log(`🌐 URL actual después del login: ${currentUrl}`);
        
        if (currentUrl.includes('/dashboards') || currentUrl.includes('/dashboard')) {
            console.log('✅ ¡Login exitoso! Redirigido al dashboard');
            
            // 7. Probar navegación al KPI dashboard
            console.log('📊 Navegando al dashboard KPI...');
            await page.goto('http://localhost:3000/dashboards/kpi');
            await page.waitForTimeout(2000);
            
            const kpiTitle = await page.locator('h1, h2, h3').first().textContent();
            console.log(`📈 Título del KPI dashboard: ${kpiTitle}`);
            
            // 8. Verificar conexión MySQL
            const connectionStatus = await page.locator('[data-testid="connection-status"], .connection-status').first();
            if (await connectionStatus.isVisible()) {
                const status = await connectionStatus.textContent();
                console.log(`🗄️ Estado de conexión MySQL: ${status}`);
            }
            
            console.log('🎉 ¡Todas las pruebas pasaron exitosamente!');
            
        } else {
            console.log('❌ Login falló - no se redirigió al dashboard');
            
            // Verificar si hay mensajes de error
            const errorMessage = await page.locator('.error, .alert, [role="alert"]').first();
            if (await errorMessage.isVisible()) {
                const error = await errorMessage.textContent();
                console.log(`❌ Mensaje de error: ${error}`);
            }
        }
        
    } catch (error) {
        console.error('❌ Error durante las pruebas:', error.message);
    } finally {
        await browser.close();
    }
}

// Ejecutar las pruebas
testVPSAuthentication()
    .then(() => {
        console.log('✅ Pruebas completadas');
        process.exit(0);
    })
    .catch((error) => {
        console.error('❌ Error fatal:', error);
        process.exit(1);
    });
