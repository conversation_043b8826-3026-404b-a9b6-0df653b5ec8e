/**
 * Página de prueba para verificar la conectividad con MySQL
 */

import React from 'react';
import {
	Container,
	Typography,
	Box,
	Paper
} from '@mui/material';
import DatabaseConnectionTest from '@/components/DatabaseConnectionTest';

/**
 * Página principal de prueba de base de datos
 */
function DatabaseTestPage() {
	return (
		<Container maxWidth="lg" sx={{ py: 4 }}>
			<Box sx={{ mb: 4 }}>
				<Typography variant="h3" component="h1" gutterBottom>
					Prueba de Conectividad MySQL
				</Typography>
				<Typography variant="h6" color="text.secondary" gutterBottom>
					Verificación del estado de conexión a la base de datos remota
				</Typography>
			</Box>

			<Paper elevation={1} sx={{ p: 3 }}>
				<DatabaseConnectionTest 
					onConnectionChange={(connected) => {
						console.log('Estado de conexión:', connected);
					}}
				/>
			</Paper>

			<Box sx={{ mt: 4 }}>
				<Typography variant="h5" gutterBottom>
					Información de Configuración
				</Typography>
				<Typography variant="body1" paragraph>
					Esta página permite verificar la conectividad con la base de datos MySQL remota.
					Los cambios implementados incluyen:
				</Typography>
				<ul>
					<li>Configuración condicional de MSW (Mock Service Worker)</li>
					<li>Variables de entorno para MySQL</li>
					<li>Proxy de desarrollo para redirigir llamadas API</li>
					<li>Servicios de conexión MySQL</li>
					<li>Configuración flexible entre desarrollo y producción</li>
				</ul>
			</Box>
		</Container>
	);
}

export default DatabaseTestPage;
