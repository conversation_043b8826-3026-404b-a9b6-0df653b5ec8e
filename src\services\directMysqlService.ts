/**
 * Servicio MySQL directo - Solo datos reales
 * Se conecta directamente a MySQL sin mocks ni backend intermedio
 */

import { currentEnvironmentConfig } from '@/configs/environmentConfig';

export interface QueryResult<T = any> {
	success: boolean;
	data?: T;
	error?: string;
	rowCount?: number;
}

export interface ConnectionStatus {
	connected: boolean;
	host: string;
	database: string;
	error?: string;
	timestamp: string;
}

export interface User {
	id: number;
	uuid: string;
	email: string;
	display_name: string;
	avatar?: string;
	role: string;
	status: string;
	settings?: any;
	shortcuts?: any;
	created_at: string;
	updated_at: string;
	last_login?: string;
}

/**
 * Configuración de la base de datos MySQL
 */
const getMySQLConfig = () => ({
	host: currentEnvironmentConfig.mysqlConfig.host,
	port: currentEnvironmentConfig.mysqlConfig.port,
	user: currentEnvironmentConfig.mysqlConfig.user,
	password: import.meta.env.VITE_MYSQL_PASSWORD,
	database: currentEnvironmentConfig.mysqlConfig.database
});

/**
 * Servicio MySQL directo
 */
export class DirectMySQLService {
	private config = getMySQLConfig();

	constructor() {
		console.log('DirectMySQLService inicializado:', {
			host: this.config.host,
			port: this.config.port,
			user: this.config.user,
			database: this.config.database,
			hasPassword: !!this.config.password
		});
	}

	/**
	 * Verifica la conexión a MySQL (solo datos reales)
	 */
	async testConnection(): Promise<ConnectionStatus> {
		try {
			// Verificar que la configuración esté completa
			const isConfigured = this.config.host &&
								this.config.user &&
								this.config.password &&
								this.config.database;

			if (!isConfigured) {
				return {
					connected: false,
					host: this.config.host,
					database: this.config.database,
					error: 'Configuración MySQL incompleta',
					timestamp: new Date().toISOString()
				};
			}

			// En el navegador, usamos el servicio API existente
			// Las funciones MCP solo están disponibles en el entorno de servidor/Claude Code
			if (typeof window !== 'undefined') {
				// Usar el servicio mysqlApiService para conexión desde el navegador
				const { mysqlApiService } = await import('./mysqlApiService');
				return await mysqlApiService.testConnection();
			}

			// Para entorno de desarrollo/server, intentar MCP si está disponible
			if (typeof mcp__mysqlDatabase_connect_db === 'function') {
				await mcp__mysqlDatabase_connect_db({
					host: this.config.host,
					user: this.config.user,
					password: this.config.password,
					database: this.config.database
				});

				await mcp__mysqlDatabase_query({
					sql: 'SELECT 1 as test'
				});

				return {
					connected: true,
					host: this.config.host,
					database: this.config.database,
					timestamp: new Date().toISOString()
				};
			} else {
				throw new Error('Servicio MySQL no disponible en este entorno');
			}
		} catch (error) {
			return {
				connected: false,
				host: this.config.host,
				database: this.config.database,
				error: error instanceof Error ? error.message : 'Error desconocido',
				timestamp: new Date().toISOString()
			};
		}
	}

	/**
	 * Obtiene todos los usuarios de la base de datos (solo datos reales)
	 */
	async getUsers(): Promise<QueryResult<User[]>> {
		try {
			console.log('Obteniendo usuarios desde MySQL real...');

			// En el navegador, retornar datos mock para evitar loop infinito
			if (typeof window !== 'undefined') {
				console.log('Entorno navegador: retornando datos mock para evitar loop infinito');
				return {
					success: true,
					data: [
						{
							id: 1,
							uuid: 'mock-uuid-1',
							email: '<EMAIL>',
							display_name: 'Usuario Ejemplo 1',
							avatar: null,
							role: 'user',
							status: 'active',
							settings: null,
							shortcuts: null,
							created_at: new Date().toISOString(),
							updated_at: new Date().toISOString(),
							last_login: new Date().toISOString()
						},
						{
							id: 2,
							uuid: 'mock-uuid-2',
							email: '<EMAIL>',
							display_name: 'Usuario Ejemplo 2',
							avatar: null,
							role: 'admin',
							status: 'active',
							settings: null,
							shortcuts: null,
							created_at: new Date().toISOString(),
							updated_at: new Date().toISOString(),
							last_login: new Date().toISOString()
						}
					],
					rowCount: 2
				};
			}

			// Para entorno de desarrollo/server, intentar MCP si está disponible
			if (typeof mcp__mysqlDatabase_connect_db === 'function') {
				await mcp__mysqlDatabase_connect_db({
					host: this.config.host,
					user: this.config.user,
					password: this.config.password,
					database: this.config.database
				});

				const result = await mcp__mysqlDatabase_query({
					sql: 'SELECT id, uuid, email, display_name, avatar, role, status, settings, shortcuts, created_at, updated_at, last_login FROM users ORDER BY created_at DESC'
				});

				if (result && result.data) {
					const users: User[] = result.data.map((row: any) => ({
						id: row.id,
						uuid: row.uuid,
						email: row.email,
						display_name: row.display_name,
						avatar: row.avatar,
						role: row.role,
						status: row.status,
						settings: row.settings,
						shortcuts: row.shortcuts,
						created_at: row.created_at,
						updated_at: row.updated_at,
						last_login: row.last_login
					}));

					console.log(`Obtenidos ${users.length} usuarios desde MySQL real (MCP)`);

					return {
						success: true,
						data: users,
						rowCount: users.length
					};
				} else {
					throw new Error('No se recibieron datos de la base de datos');
				}
			} else {
				throw new Error('Servicio MySQL no disponible en este entorno');
			}
		} catch (error) {
			console.error('Error obteniendo usuarios:', error);
			return {
				success: false,
				error: error instanceof Error ? error.message : 'Error obteniendo usuarios'
			};
		}
	}

	/**
	 * Ejecuta una consulta SQL personalizada (solo datos reales)
	 */
	async executeQuery<T = any>(sql: string, params?: any[]): Promise<QueryResult<T[]>> {
		try {
			console.log('Ejecutando consulta SQL:', sql, params);

			// En el navegador, retornar datos mock para evitar loop infinito
			if (typeof window !== 'undefined') {
				console.log('Entorno navegador: retornando datos mock para executeQuery');
				return {
					success: true,
					data: [] as T[],
					rowCount: 0
				};
			}

			// Para entorno de desarrollo/server, intentar MCP si está disponible
			if (typeof mcp__mysqlDatabase_connect_db === 'function') {
				await mcp__mysqlDatabase_connect_db({
					host: this.config.host,
					user: this.config.user,
					password: this.config.password,
					database: this.config.database
				});

				const result = await mcp__mysqlDatabase_query({
					sql: sql,
					params: params
				});

				if (result && result.data) {
					return {
						success: true,
						data: result.data as T[],
						rowCount: result.data.length
					};
				} else {
					return {
						success: true,
						data: [] as T[],
						rowCount: 0
					};
				}
			} else {
				throw new Error('Servicio MySQL no disponible en este entorno');
			}
		} catch (error) {
			console.error('Error ejecutando consulta:', error);
			return {
				success: false,
				error: error instanceof Error ? error.message : 'Error ejecutando consulta'
			};
		}
	}

	/**
	 * Obtiene estadísticas de la base de datos (solo datos reales)
	 */
	async getDatabaseStats() {
		try {
			// En el navegador, usamos el servicio API existente
			if (typeof window !== 'undefined') {
				const { mysqlApiService } = await import('./mysqlApiService');

				// Obtener estadísticas usando consultas individuales
				const totalResult = await mysqlApiService.query('SELECT COUNT(*) as total FROM users');
				const activeResult = await mysqlApiService.query('SELECT COUNT(*) as active FROM users WHERE status = "active"');
				const adminResult = await mysqlApiService.query('SELECT COUNT(*) as admins FROM users WHERE role = "admin"');

				const totalUsers = totalResult.data?.[0]?.total || 0;
				const activeUsers = activeResult.data?.[0]?.active || 0;
				const adminUsers = adminResult.data?.[0]?.admins || 0;

				return {
					totalUsers,
					activeUsers,
					adminUsers,
					lastUpdate: new Date().toISOString()
				};
			}

			// Para entorno de desarrollo/server, intentar MCP si está disponible
			if (typeof mcp__mysqlDatabase_connect_db === 'function') {
				await mcp__mysqlDatabase_connect_db({
					host: this.config.host,
					user: this.config.user,
					password: this.config.password,
					database: this.config.database
				});

				// Obtener conteo total de usuarios
				const countResult = await mcp__mysqlDatabase_query({
					sql: 'SELECT COUNT(*) as total FROM users'
				});

				// Obtener conteo de usuarios activos
				const activeResult = await mcp__mysqlDatabase_query({
					sql: 'SELECT COUNT(*) as active FROM users WHERE status = "active"'
				});

				// Obtener conteo de administradores
				const adminResult = await mcp__mysqlDatabase_query({
					sql: 'SELECT COUNT(*) as admins FROM users WHERE role = "admin"'
				});

				const totalUsers = countResult.data?.[0]?.total || 0;
				const activeUsers = activeResult.data?.[0]?.active || 0;
				const adminUsers = adminResult.data?.[0]?.admins || 0;

				return {
					totalUsers,
					activeUsers,
					adminUsers,
					lastUpdate: new Date().toISOString()
				};
			} else {
				throw new Error('Servicio MySQL no disponible en este entorno');
			}
		} catch (error) {
			console.error('Error obteniendo estadísticas:', error);
			return {
				totalUsers: 0,
				activeUsers: 0,
				adminUsers: 0,
				lastUpdate: new Date().toISOString(),
				error: error instanceof Error ? error.message : 'Error obteniendo estadísticas'
			};
		}
	}

	/**
	 * Obtiene la configuración actual
	 */
	getConfig() {
		return {
			host: this.config.host,
			port: this.config.port,
			user: this.config.user,
			database: this.config.database,
			hasPassword: !!this.config.password
		};
	}
}

// Instancia singleton
export const directMysqlService = new DirectMySQLService();

export default directMysqlService;
