/**
 * Página de Dashboard KPI con tabla de usuarios MySQL
 */

import React, { useState } from 'react';
import {
	Container,
	Typography,
	Box,
	Grid,
	Card,
	CardContent,
	CardHeader,
	Divider,
	Chip,
	Alert
} from '@mui/material';
import {
	People as PeopleIcon,
	Storage as StorageIcon,
	TrendingUp as TrendingUpIcon,
	Assessment as AssessmentIcon
} from '@mui/icons-material';

import UsersTable from './components/UsersTable';
import DatabaseConnectionTest from '@/components/DatabaseConnectionTest';
import { currentEnvironmentConfig } from '@/configs/environmentConfig';
import { directMysqlService } from '@/services/directMysqlService';

/**
 * Componente de tarjeta de estadística
 */
interface StatCardProps {
	title: string;
	value: string | number;
	icon: React.ReactNode;
	color?: 'primary' | 'secondary' | 'success' | 'warning' | 'error';
	subtitle?: string;
}

const StatCard: React.FC<StatCardProps> = ({ 
	title, 
	value, 
	icon, 
	color = 'primary',
	subtitle 
}) => (
	<Card elevation={1}>
		<CardContent>
			<Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
				<Box>
					<Typography variant="h4" component="div" color={`${color}.main`}>
						{value}
					</Typography>
					<Typography variant="h6" color="text.secondary">
						{title}
					</Typography>
					{subtitle && (
						<Typography variant="body2" color="text.secondary">
							{subtitle}
						</Typography>
					)}
				</Box>
				<Box sx={{ color: `${color}.main` }}>
					{icon}
				</Box>
			</Box>
		</CardContent>
	</Card>
);

/**
 * Página principal del Dashboard KPI
 */
function KpiDashboardPage() {
	const [userCount, setUserCount] = useState<number>(0);
	const [connectionStatus, setConnectionStatus] = useState<boolean>(false);
	const [databaseStats, setDatabaseStats] = useState<any>(null);

	const handleUserCountChange = (count: number) => {
		setUserCount(count);
	};

	const handleConnectionChange = (connected: boolean) => {
		setConnectionStatus(connected);
	};

	// Cargar estadísticas de la base de datos al montar el componente
	React.useEffect(() => {
		const loadDatabaseStats = async () => {
			try {
				const stats = await directMysqlService.getDatabaseStats();
				setDatabaseStats(stats);
				setUserCount(stats.totalUsers);
			} catch (error) {
				console.error('Error cargando estadísticas:', error);
			}
		};

		loadDatabaseStats();
	}, []);

	return (
		<Container maxWidth="xl" sx={{ py: 3 }}>
			{/* Header */}
			<Box sx={{ mb: 4 }}>
				<Typography variant="h3" component="h1" gutterBottom>
					Dashboard KPI
				</Typography>
				<Typography variant="h6" color="text.secondary" gutterBottom>
					Indicadores clave de rendimiento y datos de usuarios
				</Typography>
				
				{/* Estado de configuración */}
				<Box sx={{ mt: 2, display: 'flex', gap: 1, flexWrap: 'wrap' }}>
					<Chip
						label={currentEnvironmentConfig.isDevelopment ? 'Desarrollo' : 'Producción'}
						color={currentEnvironmentConfig.isDevelopment ? 'info' : 'success'}
						size="small"
					/>
					<Chip
						label={currentEnvironmentConfig.useRealDatabase ? 'Base de Datos Real' : 'Datos Mock'}
						color={currentEnvironmentConfig.useRealDatabase ? 'success' : 'warning'}
						size="small"
					/>
					<Chip
						label={connectionStatus ? 'Conectado' : 'Desconectado'}
						color={connectionStatus ? 'success' : 'error'}
						size="small"
					/>
				</Box>
			</Box>

			{/* Tarjetas de estadísticas */}
			<Grid container spacing={3} sx={{ mb: 4 }}>
				<Grid item xs={12} sm={6} md={3}>
					<StatCard
						title="Total Usuarios"
						value={userCount}
						icon={<PeopleIcon sx={{ fontSize: 40 }} />}
						color="primary"
						subtitle="En base de datos"
					/>
				</Grid>
				<Grid item xs={12} sm={6} md={3}>
					<StatCard
						title="Estado BD"
						value={connectionStatus ? 'Conectado' : 'Desconectado'}
						icon={<StorageIcon sx={{ fontSize: 40 }} />}
						color={connectionStatus ? 'success' : 'error'}
						subtitle="MySQL remoto"
					/>
				</Grid>
				<Grid item xs={12} sm={6} md={3}>
					<StatCard
						title="Entorno"
						value={currentEnvironmentConfig.isDevelopment ? 'DEV' : 'PROD'}
						icon={<AssessmentIcon sx={{ fontSize: 40 }} />}
						color={currentEnvironmentConfig.isDevelopment ? 'warning' : 'success'}
						subtitle="Configuración actual"
					/>
				</Grid>
				<Grid item xs={12} sm={6} md={3}>
					<StatCard
						title="Rendimiento"
						value="Óptimo"
						icon={<TrendingUpIcon sx={{ fontSize: 40 }} />}
						color="success"
						subtitle="Sistema funcionando"
					/>
				</Grid>
			</Grid>

			{/* Información de configuración */}
			{!currentEnvironmentConfig.useRealDatabase && (
				<Alert severity="warning" sx={{ mb: 3 }}>
					<Typography variant="body1">
						<strong>Modo Mock Activo:</strong> Los datos mostrados son simulados. 
						Para ver datos reales de MySQL, configura <code>VITE_USE_MSW=false</code> en tu archivo .env
					</Typography>
				</Alert>
			)}

			{/* Sección de conectividad */}
			<Grid container spacing={3} sx={{ mb: 4 }}>
				<Grid item xs={12} lg={6}>
					<Card elevation={1}>
						<CardHeader 
							title="Estado de Conectividad"
							subheader="Verificación de conexión a MySQL"
						/>
						<Divider />
						<CardContent>
							<DatabaseConnectionTest 
								onConnectionChange={handleConnectionChange}
							/>
						</CardContent>
					</Card>
				</Grid>
				<Grid item xs={12} lg={6}>
					<Card elevation={1}>
						<CardHeader 
							title="Información del Sistema"
							subheader="Configuración actual del entorno"
						/>
						<Divider />
						<CardContent>
							<Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
								<Box>
									<Typography variant="subtitle2" color="text.secondary">
										Entorno de Ejecución
									</Typography>
									<Typography variant="body1">
										{currentEnvironmentConfig.isDevelopment ? 'Desarrollo' : 'Producción'}
									</Typography>
								</Box>
								<Box>
									<Typography variant="subtitle2" color="text.secondary">
										Fuente de Datos
									</Typography>
									<Typography variant="body1">
										{currentEnvironmentConfig.useRealDatabase ? 'MySQL Remoto' : 'Datos Mock (MSW)'}
									</Typography>
								</Box>
								<Box>
									<Typography variant="subtitle2" color="text.secondary">
										Host de Base de Datos
									</Typography>
									<Typography variant="body1">
										{currentEnvironmentConfig.mysqlConfig.host}:{currentEnvironmentConfig.mysqlConfig.port}
									</Typography>
								</Box>
								<Box>
									<Typography variant="subtitle2" color="text.secondary">
										Base de Datos
									</Typography>
									<Typography variant="body1">
										{currentEnvironmentConfig.mysqlConfig.database}
									</Typography>
								</Box>
							</Box>
						</CardContent>
					</Card>
				</Grid>
			</Grid>

			{/* Tabla de usuarios */}
			<Box sx={{ mb: 4 }}>
				<Typography variant="h5" component="h2" gutterBottom>
					Datos de Usuarios
				</Typography>
				<UsersTable onUserCountChange={handleUserCountChange} />
			</Box>
		</Container>
	);
}

export default KpiDashboardPage;
