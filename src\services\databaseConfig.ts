/**
 * Configuración de base de datos MySQL
 * Maneja la configuración y conexión a la base de datos remota
 */

export interface DatabaseConfig {
	host: string;
	port: number;
	user: string;
	password: string;
	database: string;
}

export interface DatabaseConnectionOptions {
	timeout?: number;
	retries?: number;
	retryDelay?: number;
}

/**
 * Obtiene la configuración de la base de datos desde las variables de entorno
 */
export const getDatabaseConfig = (): DatabaseConfig => {
	const config: DatabaseConfig = {
		host: import.meta.env.VITE_MYSQL_HOST || 'localhost',
		port: parseInt(import.meta.env.VITE_MYSQL_PORT || '3306'),
		user: import.meta.env.VITE_MYSQL_USER || 'root',
		password: import.meta.env.VITE_MYSQL_PASSWORD || '',
		database: import.meta.env.VITE_MYSQL_DATABASE || 'test'
	};

	// Validar configuración
	if (!config.host || !config.user || !config.database) {
		throw new Error('Configuración de base de datos incompleta. Verifica las variables de entorno VITE_MYSQL_*');
	}

	return config;
};

/**
 * Genera la cadena de conexión para MySQL
 */
export const getConnectionString = (config?: DatabaseConfig): string => {
	const dbConfig = config || getDatabaseConfig();
	return `mysql://${dbConfig.user}:${dbConfig.password}@${dbConfig.host}:${dbConfig.port}/${dbConfig.database}`;
};

/**
 * Verifica si la configuración de base de datos está disponible
 */
export const isDatabaseConfigured = (): boolean => {
	try {
		getDatabaseConfig();
		return true;
	} catch {
		return false;
	}
};

/**
 * Obtiene las opciones de conexión por defecto
 */
export const getDefaultConnectionOptions = (): DatabaseConnectionOptions => ({
	timeout: 10000, // 10 segundos
	retries: 3,
	retryDelay: 1000 // 1 segundo
});

/**
 * Configuración para diferentes entornos
 */
export const getEnvironmentConfig = () => {
	const isDev = import.meta.env.DEV;
	const useMSW = import.meta.env.VITE_USE_MSW === 'true';
	
	return {
		isDevelopment: isDev,
		isProduction: !isDev,
		useMockData: isDev && useMSW,
		useRealDatabase: !isDev || !useMSW,
		backendUrl: import.meta.env.VITE_BACKEND_API_URL || 'http://localhost:3000'
	};
};

export default {
	getDatabaseConfig,
	getConnectionString,
	isDatabaseConfigured,
	getDefaultConnectionOptions,
	getEnvironmentConfig
};
