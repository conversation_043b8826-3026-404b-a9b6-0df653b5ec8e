#!/bin/bash

# Script para desplegar configuración en VPS
echo "🚀 Desplegando configuración en VPS..."

# Copiar archivo de configuración VPS
echo "📋 Copiando configuración de entorno..."
scp -i ~/.ssh/id_rsa -P 22222 .env.vps root@**************:/var/www/React_intranet_v3/.env

# Conectar al VPS y reiniciar el servidor
echo "🔄 Reiniciando servidor en VPS..."
ssh -i ~/.ssh/id_rsa -p 22222 root@************** << 'EOF'
cd /var/www/React_intranet_v3

# Matar procesos existentes de Node.js
pkill -f "npm run dev" || true
pkill -f "node" || true

# Esperar un momento
sleep 3

# Verificar que no hay procesos corriendo
ps aux | grep node | grep -v grep || echo "No hay procesos Node.js corriendo"

# Iniciar el servidor en background con screen
screen -S react-server -X quit || true
screen -S react-server -d -m npm run dev -- --host 0.0.0.0 --port 3000

# Esperar a que el servidor inicie
sleep 10

# Verificar que el servidor está corriendo
if netstat -tlnp | grep :3000; then
    echo "✅ Servidor iniciado correctamente en puerto 3000"
    echo "🌐 Accesible en: http://**************:3000"
else
    echo "❌ Error: El servidor no está corriendo en puerto 3000"
    exit 1
fi

# Mostrar logs recientes
echo "📋 Últimos logs del servidor:"
screen -S react-server -X hardcopy /tmp/react-server.log
tail -20 /tmp/react-server.log 2>/dev/null || echo "No se pudieron obtener los logs"

EOF

echo "✅ Despliegue completado!"
echo "🌐 La aplicación debería estar disponible en: http://**************:3000"
echo "🔍 Para verificar el estado del servidor, usa: ssh -i ~/.ssh/id_rsa -p 22222 root@************** 'screen -list'"
