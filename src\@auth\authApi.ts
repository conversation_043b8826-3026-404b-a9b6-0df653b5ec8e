import { User } from '@auth/user';
import UserModel from '@auth/user/models/UserModel';
import { PartialDeep } from 'type-fest';
import api from '@/utils/api';

type AuthResponse = {
	user: User;
	access_token: string;
};

/**
 * Refreshes the access token
 */
export async function authRefreshToken(): Promise<Response> {
	return api.post('mock/auth/refresh', {
		retry: 0 // Don't retry refresh token requests
	});
}

/**
 * Sign in with token
 */
export async function authSignInWithToken(accessToken: string): Promise<Response> {
	// En modo desarrollo, validar tokens mock
	if (import.meta.env.DEV && accessToken.startsWith('dev-token-')) {
		console.log('Validando token de desarrollo:', accessToken);

		// Crear respuesta mock para token de desarrollo
		const mockUser: User = UserModel({
			id: 'dev-user-001',
			displayName: 'Usuario Desarrollo',
			email: '<EMAIL>',
			avatar: '',
			role: 'admin',
			data: {
				displayName: 'Usuario Desarrollo',
				email: '<EMAIL>'
			}
		});

		// Simular respuesta exitosa
		return new Response(JSON.stringify(mockUser), {
			status: 200,
			headers: { 'Content-Type': 'application/json' }
		});
	}

	// En producción, usar autenticación normal
	return api.get('mock/auth/sign-in-with-token', {
		headers: { Authorization: `Bearer ${accessToken}` }
	});
}

/**
 * Sign in
 */
export async function authSignIn(credentials: { email: string; password: string }): Promise<AuthResponse> {
	// En modo desarrollo, aceptar cualquier combinación de email/password
	if (import.meta.env.DEV) {
		console.log('Modo desarrollo: autenticación simplificada activada');

		// Validar que al menos tengan contenido
		if (!credentials.email || !credentials.password) {
			throw new Error('Email y contraseña son requeridos');
		}

		// Crear usuario mock para desarrollo
		const mockUser: User = UserModel({
			id: 'dev-user-001',
			displayName: 'Usuario Desarrollo',
			email: credentials.email,
			avatar: '',
			role: 'admin',
			data: {
				displayName: 'Usuario Desarrollo',
				email: credentials.email
			}
		});

		// Generar token mock
		const mockToken = `dev-token-${Date.now()}`;

		return {
			user: mockUser,
			access_token: mockToken
		};
	}

	// En producción, usar autenticación normal
	return api
		.post('mock/auth/sign-in', {
			json: credentials
		})
		.json();
}

/**
 * Sign up
 */
export async function authSignUp(data: {
	displayName: string;
	email: string;
	password: string;
}): Promise<AuthResponse> {
	return api
		.post('mock/auth/sign-up', {
			json: data
		})
		.json();
}

/**
 * Get user by id
 */
export async function authGetDbUser(userId: string): Promise<User> {
	return api.get(`mock/auth/user/${userId}`).json();
}

/**
 * Get user by email
 */
export async function authGetDbUserByEmail(email: string): Promise<User> {
	return api.get(`mock/auth/user-by-email/${email}`).json();
}

/**
 * Update user
 */
export function authUpdateDbUser(user: PartialDeep<User>): Promise<Response> {
	return api.put(`mock/auth/user/${user.id}`, {
		json: UserModel(user)
	});
}

/**
 * Create user
 */
export async function authCreateDbUser(user: PartialDeep<User>): Promise<User> {
	return api
		.post('mock/users', {
			json: UserModel(user)
		})
		.json();
}
