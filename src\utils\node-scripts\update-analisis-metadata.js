#!/usr/bin/env node
const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

function getGitCommitHash() {
  try {
    return execSync('git rev-parse --short HEAD', { encoding: 'utf8' }).trim();
  } catch (e) {
    return null;
  }
}

function getPackageVersion(projectRoot) {
  try {
    const pkg = require(path.join(projectRoot, 'package.json'));
    return pkg.version || null;
  } catch (e) {
    return null;
  }
}

function isoNow() {
  return new Date().toISOString();
}

function updateFile(filePath, versionLabel) {
  const content = fs.readFileSync(filePath, 'utf8');

  const metadataRegex = /\*Version: .* \| Generated: .*\*/i;
  const newMetadata = `*Version: ${versionLabel} | Generated: ${isoNow()}*`;

  let newContent;
  if (metadataRegex.test(content)) {
    newContent = content.replace(metadataRegex, newMetadata);
  } else {
    // append before final closing backticks or at end
    if (content.trim().endsWith('```')) {
      // insert before the last code fence
      const idx = content.lastIndexOf('```');
      newContent = content.slice(0, idx) + newMetadata + '\n' + content.slice(idx);
    } else {
      newContent = content + '\n' + newMetadata + '\n';
    }
  }

  fs.writeFileSync(filePath, newContent, 'utf8');
  console.log(`Updated metadata in ${filePath}`);
}

function main() {
  const projectRoot = path.resolve(__dirname, '..', '..', '..', '..');
  const analysFile = path.join(projectRoot, 'ANALISIS_PROYECTO.md');

  if (!fs.existsSync(analysFile)) {
    console.error('ANALISIS_PROYECTO.md not found at', analysFile);
    process.exit(2);
  }

  const commit = getGitCommitHash();
  const version = getPackageVersion(projectRoot);

  const label = commit ? `${version || '0.0.0'}+${commit}` : (version || '0.0.0');

  updateFile(analysFile, label);
}

main();
