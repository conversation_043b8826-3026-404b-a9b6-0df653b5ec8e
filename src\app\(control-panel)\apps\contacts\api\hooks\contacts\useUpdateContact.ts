import { useMutation, useQueryClient } from '@tanstack/react-query';
import { contactsApi } from '../../services/contactsApiService';
import { contactsListQueryKey } from './useContactsList';
import { contactQuery<PERSON>ey } from './useContact';

export const useUpdateContact = () => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: contactsApi.updateContact,
		onSuccess: (_, contact) => {
			queryClient.invalidateQueries({ queryKey: contactsListQueryKey });
			queryClient.invalidateQueries({ queryKey: contactQ<PERSON>y<PERSON><PERSON>(contact.id) });
		}
	});
};
