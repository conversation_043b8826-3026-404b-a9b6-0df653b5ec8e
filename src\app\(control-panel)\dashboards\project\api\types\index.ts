import BudgetDetailsDataType from './budget/BudgetDetailsDataType';
import BudgetDistributionDataType from './budget/BudgetDistributionDataType';
import ExpensesDataType from './budget/ExpensesDataType';
import WidgetDataType from './home/<USER>';
import GithubIssuesDataType from './home/<USER>';
import ScheduleDataType from './home/<USER>';
import TaskDistributionDataType from './home/<USER>';
import TeamMemberType from './team/TeamMemberType';

export type ProjectDashboardWidgetType =
	| BudgetDetailsDataType
	| BudgetDistributionDataType
	| ExpensesDataType
	| WidgetDataType
	| GithubIssuesDataType
	| ScheduleDataType
	| TaskDistributionDataType
	| TeamMemberType[];

export type ProjectType = {
	id: number;
	name: string;
};
