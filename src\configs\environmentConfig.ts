/**
 * Configuración de entorno centralizada
 * Maneja la configuración para desarrollo, testing y producción
 */

export interface EnvironmentConfig {
	// Información del entorno
	isDevelopment: boolean;
	isProduction: boolean;
	isTesting: boolean;
	
	// Configuración de APIs
	apiBaseUrl: string;
	backendApiUrl: string;
	
	// Configuración de MSW
	useMSW: boolean;
	bypassMSWForExternal: boolean;
	
	// Configuración de base de datos
	useRealDatabase: boolean;
	mysqlConfig: {
		host: string;
		port: number;
		user: string;
		database: string;
		// password no se expone por seguridad
	};
	
	// Configuración de logging
	enableLogging: boolean;
	logLevel: 'debug' | 'info' | 'warn' | 'error';
}

/**
 * Obtiene la configuración completa del entorno actual
 */
export const getEnvironmentConfig = (): EnvironmentConfig => {
	const isDev = import.meta.env.DEV;
	const isProd = import.meta.env.PROD;
	const useMSW = import.meta.env.VITE_USE_MSW === 'true';
	const bypassExternal = import.meta.env.VITE_BYPASS_MSW_FOR_EXTERNAL === 'true';
	
	return {
		// Entorno
		isDevelopment: isDev,
		isProduction: isProd,
		isTesting: import.meta.env.MODE === 'test',
		
		// APIs
		apiBaseUrl: import.meta.env.VITE_API_BASE_URL || 'http://localhost:3000',
		backendApiUrl: import.meta.env.VITE_BACKEND_API_URL || 'http://**************:8080',
		
		// MSW
		useMSW: isDev && useMSW,
		bypassMSWForExternal: bypassExternal,
		
		// Base de datos
		useRealDatabase: !isDev || !useMSW,
		mysqlConfig: {
			host: import.meta.env.VITE_MYSQL_HOST || 'localhost',
			port: parseInt(import.meta.env.VITE_MYSQL_PORT || '3306'),
			user: import.meta.env.VITE_MYSQL_USER || 'root',
			database: import.meta.env.VITE_MYSQL_DATABASE || 'test'
		},
		
		// Logging
		enableLogging: isDev || import.meta.env.VITE_ENABLE_LOGGING === 'true',
		logLevel: (import.meta.env.VITE_LOG_LEVEL as any) || (isDev ? 'debug' : 'error')
	};
};

/**
 * Configuraciones predefinidas para diferentes entornos
 */
export const environmentPresets = {
	development: {
		useMSW: true,
		useRealDatabase: false,
		enableLogging: true,
		logLevel: 'debug' as const
	},
	
	developmentWithRealDB: {
		useMSW: false,
		useRealDatabase: true,
		enableLogging: true,
		logLevel: 'debug' as const
	},
	
	production: {
		useMSW: false,
		useRealDatabase: true,
		enableLogging: false,
		logLevel: 'error' as const
	},
	
	testing: {
		useMSW: true,
		useRealDatabase: false,
		enableLogging: true,
		logLevel: 'warn' as const
	}
};

/**
 * Aplica un preset de configuración
 */
export const applyEnvironmentPreset = (preset: keyof typeof environmentPresets) => {
	const config = environmentPresets[preset];
	
	// En un entorno real, esto podría actualizar variables de entorno
	// Por ahora, solo retornamos la configuración
	return {
		...getEnvironmentConfig(),
		...config
	};
};

/**
 * Valida que la configuración del entorno sea correcta
 */
export const validateEnvironmentConfig = (config: EnvironmentConfig): string[] => {
	const errors: string[] = [];
	
	if (config.useRealDatabase) {
		if (!config.mysqlConfig.host) {
			errors.push('VITE_MYSQL_HOST es requerido para usar base de datos real');
		}
		if (!config.mysqlConfig.user) {
			errors.push('VITE_MYSQL_USER es requerido para usar base de datos real');
		}
		if (!config.mysqlConfig.database) {
			errors.push('VITE_MYSQL_DATABASE es requerido para usar base de datos real');
		}
		if (!config.backendApiUrl) {
			errors.push('VITE_BACKEND_API_URL es requerido para usar base de datos real');
		}
	}
	
	return errors;
};

/**
 * Logger condicional basado en la configuración del entorno
 */
export const createEnvironmentLogger = (config: EnvironmentConfig) => {
	const shouldLog = (level: string) => {
		if (!config.enableLogging) return false;
		
		const levels = ['debug', 'info', 'warn', 'error'];
		const currentLevelIndex = levels.indexOf(config.logLevel);
		const messageLevelIndex = levels.indexOf(level);
		
		return messageLevelIndex >= currentLevelIndex;
	};
	
	return {
		debug: (...args: any[]) => shouldLog('debug') && console.debug('[ENV-DEBUG]', ...args),
		info: (...args: any[]) => shouldLog('info') && console.info('[ENV-INFO]', ...args),
		warn: (...args: any[]) => shouldLog('warn') && console.warn('[ENV-WARN]', ...args),
		error: (...args: any[]) => shouldLog('error') && console.error('[ENV-ERROR]', ...args)
	};
};

// Instancia global de configuración
export const currentEnvironmentConfig = getEnvironmentConfig();
export const environmentLogger = createEnvironmentLogger(currentEnvironmentConfig);

// Validar configuración al cargar
const configErrors = validateEnvironmentConfig(currentEnvironmentConfig);
if (configErrors.length > 0) {
	environmentLogger.warn('Errores en configuración de entorno:', configErrors);
}

export default {
	getEnvironmentConfig,
	environmentPresets,
	applyEnvironmentPreset,
	validateEnvironmentConfig,
	createEnvironmentLogger,
	currentEnvironmentConfig,
	environmentLogger
};
