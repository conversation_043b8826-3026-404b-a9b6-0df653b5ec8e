/**
 * Servicio API para conexiones MySQL
 * Maneja las llamadas a la API del backend que se conecta a MySQL
 */

import { api } from '@/utils/api';
import { getEnvironmentConfig, getDatabaseConfig } from './databaseConfig';

export interface QueryResult<T = any> {
	success: boolean;
	data?: T;
	error?: string;
	rowCount?: number;
}

export interface ConnectionStatus {
	connected: boolean;
	host: string;
	database: string;
	error?: string;
	timestamp: string;
}

/**
 * Servicio principal para interactuar con MySQL a través del backend
 */
export class MySQLApiService {
	private baseUrl: string;
	private config = getEnvironmentConfig();

	constructor() {
		// Siempre usar conexión directa a MySQL, ignorar el backend API
		this.baseUrl = 'mysql';
		console.log('MySQLApiService inicializado:', {
			baseUrl: this.baseUrl,
			useRealDatabase: this.config.useRealDatabase,
			useMockData: this.config.useMockData
		});
	}

	/**
	 * Verifica la conexión a la base de datos (solo conexión directa)
	 */
	async testConnection(): Promise<ConnectionStatus> {
		try {
			// Usar siempre el servicio directo para conexión real
			const { directMysqlService } = await import('./directMysqlService');
			return await directMysqlService.testConnection();
		} catch (error) {
			console.error('Error al probar conexión:', error);
			return {
				connected: false,
				host: getDatabaseConfig().host,
				database: getDatabaseConfig().database,
				error: error instanceof Error ? error.message : 'Error desconocido',
				timestamp: new Date().toISOString()
			};
		}
	}

	/**
	 * Ejecuta una consulta SELECT (solo datos reales)
	 */
	async query<T = any>(sql: string, params?: any[]): Promise<QueryResult<T[]>> {
		try {
			// Usar siempre el servicio directo para conexión real a MySQL
			const { directMysqlService } = await import('./directMysqlService');
			return await directMysqlService.executeQuery<T>(sql, params);
		} catch (error) {
			console.error('Error en consulta:', error);
			return {
				success: false,
				error: error instanceof Error ? error.message : 'Error en consulta'
			};
		}
	}

	/**
	 * Ejecuta una consulta de modificación (INSERT, UPDATE, DELETE) - solo datos reales
	 */
	async execute(sql: string, params?: any[]): Promise<QueryResult> {
		try {
			// Usar siempre el servicio directo para conexión real a MySQL
			const { directMysqlService } = await import('./directMysqlService');
			return await directMysqlService.executeQuery(sql, params);
		} catch (error) {
			console.error('Error en ejecución:', error);
			return {
				success: false,
				error: error instanceof Error ? error.message : 'Error en ejecución'
			};
		}
	}

	/**
	 * Obtiene información de las tablas de la base de datos
	 */
	async getTables(): Promise<QueryResult<string[]>> {
		const sql = 'SHOW TABLES';
		return this.query<{ [key: string]: string }>(sql).then(result => ({
			...result,
			data: result.data?.map(row => Object.values(row)[0]) || []
		}));
	}

	/**
	 * Obtiene la estructura de una tabla
	 */
	async getTableStructure(tableName: string): Promise<QueryResult> {
		const sql = 'DESCRIBE ??';
		return this.query(sql, [tableName]);
	}

	/**
	 * Verifica si el servicio está usando datos reales o mock
	 */
	isUsingRealDatabase(): boolean {
		return this.config.useRealDatabase;
	}

	/**
	 * Obtiene el estado actual del servicio
	 */
	getServiceStatus() {
		return {
			...this.config,
			baseUrl: this.baseUrl,
			databaseConfig: this.config.useRealDatabase ? getDatabaseConfig() : null
		};
	}
}

// Instancia singleton del servicio
export const mysqlApiService = new MySQLApiService();

export default mysqlApiService;
