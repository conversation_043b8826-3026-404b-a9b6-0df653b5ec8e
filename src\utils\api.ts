import ky, { KyInstance } from 'ky';

// Configuración inteligente de URLs basada en el entorno
const getApiBaseUrl = () => {
	const useMSW = import.meta.env.VITE_USE_MSW === 'true' && import.meta.env.DEV;
	const backendApiUrl = import.meta.env.VITE_BACKEND_API_URL;
	const defaultApiUrl = import.meta.env.VITE_API_BASE_URL || 'http://localhost:3000';

	if (import.meta.env.DEV) {
		// En desarrollo, usar backend remoto si MSW está deshabilitado
		if (!useMSW && backendApiUrl) {
			console.log('Usando backend remoto:', backendApiUrl);
			return backendApiUrl;
		}

		// Si MSW está habilitado, usar localhost para interceptar
		const apiUrl = new URL(defaultApiUrl);
		const devApiBaseHost = apiUrl.hostname;
		const PORT = Number(import.meta.env.VITE_PORT) || 3000;
		const devApiBaseUrl = `${apiUrl.protocol}//${devApiBaseHost}:${PORT}`;
		console.log('Usando servidor de desarrollo:', devApiBaseUrl);
		return devApiBaseUrl;
	}

	// En producción, usar la URL del backend remoto
	const productionUrl = backendApiUrl || defaultApiUrl || '/';
	console.log('Usando URL de producción:', productionUrl);
	return productionUrl;
};

export const API_BASE_URL = getApiBaseUrl();

let globalHeaders: Record<string, string> = {};

export const api: KyInstance = ky.create({
	prefixUrl: `${API_BASE_URL}/api`,
	hooks: {
		beforeRequest: [
			(request) => {
				Object.entries(globalHeaders).forEach(([key, value]) => {
					request.headers.set(key, value);
				});
			}
		]
	},
	retry: {
		limit: 2,
		methods: ['get', 'put', 'head', 'delete', 'options', 'trace']
	}
});

export const setGlobalHeaders = (headers: Record<string, string>) => {
	globalHeaders = { ...globalHeaders, ...headers };
};

export const removeGlobalHeaders = (headerKeys: string[]) => {
	headerKeys.forEach((key) => {
		delete globalHeaders[key];
	});
};

export const getGlobalHeaders = () => {
	return globalHeaders;
};

export default api;
