/**
 * Componente para probar la conectividad con la base de datos MySQL
 */

import React, { useState, useEffect } from 'react';
import {
	Card,
	CardContent,
	CardHeader,
	Typography,
	Button,
	Box,
	Alert,
	CircularProgress,
	Chip,
	Divider,
	List,
	ListItem,
	ListItemText
} from '@mui/material';
import {
	CheckCircle as CheckCircleIcon,
	Error as ErrorIcon,
	Refresh as RefreshIcon,
	Storage as StorageIcon
} from '@mui/icons-material';

import { directMysqlService, ConnectionStatus } from '@/services/directMysqlService';
import { currentEnvironmentConfig, environmentLogger } from '@/configs/environmentConfig';

interface DatabaseConnectionTestProps {
	onConnectionChange?: (connected: boolean) => void;
}

export const DatabaseConnectionTest: React.FC<DatabaseConnectionTestProps> = ({
	onConnectionChange
}) => {
	const [connectionStatus, setConnectionStatus] = useState<ConnectionStatus | null>(null);
	const [isLoading, setIsLoading] = useState(false);
	const [tables, setTables] = useState<string[]>([]);
	const [error, setError] = useState<string | null>(null);

	const testConnection = async () => {
		setIsLoading(true);
		setError(null);
		environmentLogger.info('Iniciando prueba de conexión...');

		try {
			const status = await directMysqlService.testConnection();
			setConnectionStatus(status);
			onConnectionChange?.(status.connected);

			if (status.connected) {
				// Si la conexión es exitosa, simular tablas disponibles
				setTables(['users', 'notifications', 'system_settings', 'user_sessions']);
			}

			environmentLogger.info('Prueba de conexión completada:', status);
		} catch (err) {
			const errorMessage = err instanceof Error ? err.message : 'Error desconocido';
			setError(errorMessage);
			environmentLogger.error('Error en prueba de conexión:', err);
		} finally {
			setIsLoading(false);
		}
	};

	useEffect(() => {
		testConnection();
	}, []);

	const getStatusColor = () => {
		if (!connectionStatus) return 'default';
		return connectionStatus.connected ? 'success' : 'error';
	};

	const getStatusIcon = () => {
		if (isLoading) return <CircularProgress size={20} />;
		if (!connectionStatus) return <StorageIcon />;
		return connectionStatus.connected ? <CheckCircleIcon /> : <ErrorIcon />;
	};

	const serviceStatus = directMysqlService.getConfig();

	return (
		<Card sx={{ maxWidth: 600, margin: 'auto' }}>
			<CardHeader
				title="Prueba de Conectividad MySQL"
				subheader="Verificación del estado de conexión a la base de datos"
				action={
					<Button
						variant="outlined"
						onClick={testConnection}
						disabled={isLoading}
						startIcon={<RefreshIcon />}
					>
						Probar Conexión
					</Button>
				}
			/>
			<CardContent>
				{/* Estado del Entorno */}
				<Box sx={{ mb: 3 }}>
					<Typography variant="h6" gutterBottom>
						Configuración del Entorno
					</Typography>
					<Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap', mb: 2 }}>
						<Chip
							label={serviceStatus.isDevelopment ? 'Desarrollo' : 'Producción'}
							color={serviceStatus.isDevelopment ? 'info' : 'success'}
							size="small"
						/>
						<Chip
							label={serviceStatus.useMockData ? 'Datos Mock' : 'Base de Datos Real'}
							color={serviceStatus.useMockData ? 'warning' : 'success'}
							size="small"
						/>
						<Chip
							label={`MSW: ${serviceStatus.useMockData ? 'Activo' : 'Inactivo'}`}
							color={serviceStatus.useMockData ? 'primary' : 'default'}
							size="small"
						/>
					</Box>
					<Typography variant="body2" color="text.secondary">
						URL Base: {serviceStatus.baseUrl}
					</Typography>
				</Box>

				<Divider sx={{ my: 2 }} />

				{/* Estado de Conexión */}
				<Box sx={{ mb: 3 }}>
					<Typography variant="h6" gutterBottom>
						Estado de Conexión
					</Typography>
					
					{error && (
						<Alert severity="error" sx={{ mb: 2 }}>
							{error}
						</Alert>
					)}

					{connectionStatus && (
						<Box>
							<Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 2 }}>
								{getStatusIcon()}
								<Chip
									label={connectionStatus.connected ? 'Conectado' : 'Desconectado'}
									color={getStatusColor()}
									variant="filled"
								/>
							</Box>
							
							<List dense>
								<ListItem>
									<ListItemText
										primary="Host"
										secondary={connectionStatus.host}
									/>
								</ListItem>
								<ListItem>
									<ListItemText
										primary="Base de Datos"
										secondary={connectionStatus.database}
									/>
								</ListItem>
								<ListItem>
									<ListItemText
										primary="Última Verificación"
										secondary={new Date(connectionStatus.timestamp).toLocaleString()}
									/>
								</ListItem>
								{connectionStatus.error && (
									<ListItem>
										<ListItemText
											primary="Error"
											secondary={connectionStatus.error}
										/>
									</ListItem>
								)}
							</List>
						</Box>
					)}
				</Box>

				{/* Lista de Tablas */}
				{tables.length > 0 && (
					<Box>
						<Divider sx={{ my: 2 }} />
						<Typography variant="h6" gutterBottom>
							Tablas Disponibles ({tables.length})
						</Typography>
						<Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
							{tables.map((table) => (
								<Chip
									key={table}
									label={table}
									variant="outlined"
									size="small"
								/>
							))}
						</Box>
					</Box>
				)}

				{/* Información de Configuración */}
				{serviceStatus.databaseConfig && (
					<Box sx={{ mt: 3 }}>
						<Divider sx={{ my: 2 }} />
						<Typography variant="h6" gutterBottom>
							Configuración de Base de Datos
						</Typography>
						<List dense>
							<ListItem>
								<ListItemText
									primary="Host"
									secondary={`${serviceStatus.databaseConfig.host}:${serviceStatus.databaseConfig.port}`}
								/>
							</ListItem>
							<ListItem>
								<ListItemText
									primary="Usuario"
									secondary={serviceStatus.databaseConfig.user}
								/>
							</ListItem>
							<ListItem>
								<ListItemText
									primary="Base de Datos"
									secondary={serviceStatus.databaseConfig.database}
								/>
							</ListItem>
						</List>
					</Box>
				)}
			</CardContent>
		</Card>
	);
};

export default DatabaseConnectionTest;
