import { useMutation, useQueryClient } from '@tanstack/react-query';
import { messengerApiService } from '../../services/messengerApiService';
import { contactQuery<PERSON>ey } from './useContact';
import { contactsQuery<PERSON>ey } from './useContacts';

export function useUpdateContact() {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: messengerApiService.contacts.update,
		onSuccess: (_, variables) => {
			queryClient.invalidateQueries({ queryKey: contactsQuery<PERSON>ey });
			queryClient.invalidateQueries({ queryKey: contactQ<PERSON>y<PERSON><PERSON>(variables.id) });
		}
	});
}
