/**
 * Tabla de usuarios con datos de MySQL
 */

import React, { useState, useEffect } from 'react';
import {
	Table,
	TableBody,
	TableCell,
	TableContainer,
	TableHead,
	TableRow,
	Paper,
	CircularProgress,
	Alert,
	Typography,
	Box,
	Chip,
	Avatar,
	IconButton,
	Tooltip,
	TablePagination
} from '@mui/material';
import {
	Refresh as RefreshIcon,
	Person as PersonIcon,
	Email as EmailIcon,
	CalendarToday as CalendarIcon
} from '@mui/icons-material';

import { directMysqlService, QueryResult, User as MySQLUser } from '@/services/directMysqlService';

// Usar la interfaz User del servicio MySQL directo
type User = MySQLUser;

interface UsersTableProps {
	onUserCountChange?: (count: number) => void;
}

export const UsersTable: React.FC<UsersTableProps> = ({ onUserCountChange }) => {
	const [users, setUsers] = useState<User[]>([]);
	const [loading, setLoading] = useState(true);
	const [error, setError] = useState<string | null>(null);
	const [page, setPage] = useState(0);
	const [rowsPerPage, setRowsPerPage] = useState(10);

	const fetchUsers = async () => {
		setLoading(true);
		setError(null);

		try {
			console.log('Obteniendo usuarios desde MySQL directo...');

			// Obtener usuarios directamente de MySQL
			const result = await directMysqlService.getUsers();

			if (result.success && result.data) {
				setUsers(result.data);
				onUserCountChange?.(result.data.length);
				console.log(`${result.data.length} usuarios obtenidos exitosamente`);
			} else {
				setError(result.error || 'Error al obtener usuarios');
				console.error('Error en resultado:', result.error);
			}
		} catch (err) {
			console.error('Error fetching users:', err);
			setError(err instanceof Error ? err.message : 'Error desconocido');
		} finally {
			setLoading(false);
		}
	};

	useEffect(() => {
		fetchUsers();
	}, []);

	const handleChangePage = (event: unknown, newPage: number) => {
		setPage(newPage);
	};

	const handleChangeRowsPerPage = (event: React.ChangeEvent<HTMLInputElement>) => {
		setRowsPerPage(parseInt(event.target.value, 10));
		setPage(0);
	};

	const formatDate = (dateString?: string) => {
		if (!dateString) return 'N/A';
		try {
			return new Date(dateString).toLocaleDateString('es-ES', {
				year: 'numeric',
				month: 'short',
				day: 'numeric',
				hour: '2-digit',
				minute: '2-digit'
			});
		} catch {
			return dateString;
		}
	};

	const getStatusColor = (status?: string) => {
		switch (status?.toLowerCase()) {
			case 'active':
			case 'activo':
				return 'success';
			case 'inactive':
			case 'inactivo':
				return 'error';
			case 'pending':
			case 'pendiente':
				return 'warning';
			default:
				return 'default';
		}
	};

	const getRoleColor = (role?: string) => {
		switch (role?.toLowerCase()) {
			case 'admin':
			case 'administrator':
				return 'error';
			case 'user':
			case 'usuario':
				return 'primary';
			case 'moderator':
			case 'moderador':
				return 'warning';
			default:
				return 'default';
		}
	};

	if (loading) {
		return (
			<Box display="flex" justifyContent="center" alignItems="center" minHeight={200}>
				<CircularProgress />
				<Typography variant="body1" sx={{ ml: 2 }}>
					Cargando usuarios...
				</Typography>
			</Box>
		);
	}

	if (error) {
		return (
			<Alert 
				severity="error" 
				action={
					<IconButton
						color="inherit"
						size="small"
						onClick={fetchUsers}
					>
						<RefreshIcon />
					</IconButton>
				}
			>
				<Typography variant="body1">
					Error al cargar usuarios: {error}
				</Typography>
			</Alert>
		);
	}

	if (users.length === 0) {
		return (
			<Alert severity="info">
				<Typography variant="body1">
					No se encontraron usuarios en la base de datos.
				</Typography>
			</Alert>
		);
	}

	// Paginar usuarios
	const paginatedUsers = users.slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage);

	return (
		<Paper elevation={1}>
			<Box sx={{ p: 2, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
				<Typography variant="h6" component="h2">
					Usuarios de la Base de Datos
				</Typography>
				<Tooltip title="Actualizar datos">
					<IconButton onClick={fetchUsers} disabled={loading}>
						<RefreshIcon />
					</IconButton>
				</Tooltip>
			</Box>

			<TableContainer>
				<Table>
					<TableHead>
						<TableRow>
							<TableCell>Usuario</TableCell>
							<TableCell>Email</TableCell>
							<TableCell>Estado</TableCell>
							<TableCell>Rol</TableCell>
							<TableCell>Creado</TableCell>
							<TableCell>Actualizado</TableCell>
						</TableRow>
					</TableHead>
					<TableBody>
						{paginatedUsers.map((user) => (
							<TableRow key={user.id} hover>
								<TableCell>
									<Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
										<Avatar 
											src={user.avatar} 
											sx={{ width: 32, height: 32 }}
										>
											<PersonIcon />
										</Avatar>
										<Box>
											<Typography variant="body2" fontWeight="medium">
												{user.display_name || `Usuario ${user.id}`}
											</Typography>
											<Typography variant="caption" color="text.secondary">
												ID: {user.id}
											</Typography>
										</Box>
									</Box>
								</TableCell>
								<TableCell>
									<Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
										<EmailIcon fontSize="small" color="action" />
										<Typography variant="body2">
											{user.email || 'N/A'}
										</Typography>
									</Box>
								</TableCell>
								<TableCell>
									<Chip
										label={user.status || 'N/A'}
										color={getStatusColor(user.status) as any}
										size="small"
									/>
								</TableCell>
								<TableCell>
									<Chip
										label={user.role || 'N/A'}
										color={getRoleColor(user.role) as any}
										size="small"
										variant="outlined"
									/>
								</TableCell>
								<TableCell>
									<Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
										<CalendarIcon fontSize="small" color="action" />
										<Typography variant="body2">
											{formatDate(user.created_at)}
										</Typography>
									</Box>
								</TableCell>
								<TableCell>
									<Typography variant="body2">
										{formatDate(user.updated_at)}
									</Typography>
								</TableCell>
							</TableRow>
						))}
					</TableBody>
				</Table>
			</TableContainer>

			<TablePagination
				rowsPerPageOptions={[5, 10, 25, 50]}
				component="div"
				count={users.length}
				rowsPerPage={rowsPerPage}
				page={page}
				onPageChange={handleChangePage}
				onRowsPerPageChange={handleChangeRowsPerPage}
				labelRowsPerPage="Filas por página:"
				labelDisplayedRows={({ from, to, count }) => 
					`${from}-${to} de ${count !== -1 ? count : `más de ${to}`}`
				}
			/>
		</Paper>
	);
};

export default UsersTable;
