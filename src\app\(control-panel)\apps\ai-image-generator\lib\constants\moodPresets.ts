import { StyleOption } from '../../components/forms/generator-form/controllers/StyleSelectFormController';

const moodPresets: StyleOption[] = [
	{
		value: 'dramatic',
		label: 'Dramatic',
		description: 'Intense and emotionally charged scene with dramatic lighting and shadows',
		image: '/assets/images/apps/ai-image-generator/moods/dramatic.webp',
		color: '#881c1f'
	},
	{
		value: 'peaceful',
		label: 'Peaceful',
		description: 'Calm and serene scene with soft lighting and a sense of tranquility',
		image: '/assets/images/apps/ai-image-generator/moods/peaceful.webp',
		color: '#b19369'
	},
	{
		value: 'melancholy',
		label: 'Melancholy',
		description: 'A moody and introspective scene with soft lighting and a sense of melancholy',
		image: '/assets/images/apps/ai-image-generator/moods/melancholy.webp',
		color: '#607D8B'
	},
	{
		value: 'ethereal',
		label: 'Ethereal',
		description: 'A mystical and ethereal scene with soft lighting and a sense of mystery',
		image: '/assets/images/apps/ai-image-generator/moods/ethereal.webp',
		color: '#597180'
	},
	{
		value: 'mysterious',
		label: 'Mysterious',
		description: 'A mysterious and enigmatic scene with soft lighting and a sense of intrigue',
		image: '/assets/images/apps/ai-image-generator/moods/mysterious.webp',
		color: '#506f6d'
	},
	{
		value: 'serene',
		label: 'Serene',
		description: 'A serene and tranquil scene with soft lighting and a sense of calmness',
		image: '/assets/images/apps/ai-image-generator/moods/serene.webp',
		color: '#f6f1de'
	},
	{
		value: 'energetic',
		label: 'Energetic',
		description: 'An energetic and vibrant scene with bold colors and dynamic lighting',
		image: '/assets/images/apps/ai-image-generator/moods/energetic.webp',
		color: '#dc7249'
	},
	{
		value: 'romantic',
		label: 'Romantic',
		description: 'A romantic and passionate scene with soft lighting and a sense of romance',
		image: '/assets/images/apps/ai-image-generator/moods/romantic.webp',
		color: '#a03c26'
	},
	{
		value: 'dark_fantasy',
		label: 'Dark Fantasy',
		description: 'A dark and mysterious scene with soft lighting and a sense of fantasy',
		image: '/assets/images/apps/ai-image-generator/moods/dark_fantasy.webp',
		color: '#71817e'
	},
	{
		value: 'adventurous',
		label: 'Adventurous',
		description: 'An adventurous and exciting scene with bold colors and dynamic lighting',
		image: '/assets/images/apps/ai-image-generator/moods/adventurous.webp',
		color: '#b4f8e6'
	},
	{
		value: 'introspective',
		label: 'Introspective',
		description: 'An introspective and contemplative scene with soft lighting and a sense of introspection',
		image: '/assets/images/apps/ai-image-generator/moods/introspective.webp',
		color: '#8b7955'
	},
	{
		value: 'heroic',
		label: 'Heroic',
		description: 'A heroic and inspiring scene with bold colors and dynamic lighting',
		image: '/assets/images/apps/ai-image-generator/moods/heroic.webp',
		color: '#2e5594'
	},
	{
		value: 'playful',
		label: 'Playful',
		description: 'A playful and whimsical scene with bold colors and dynamic lighting',
		image: '/assets/images/apps/ai-image-generator/moods/playful.webp',
		color: '#de7b62'
	},
	{
		value: 'nostalgic',
		label: 'Nostalgic',
		description: 'A nostalgic and sentimental scene with soft lighting and a sense of nostalgia',
		image: '/assets/images/apps/ai-image-generator/moods/nostalgic.webp',
		color: '#8D6E63'
	},
	{
		value: 'mystical',
		label: 'Mystical',
		description: 'A mystical and ethereal scene with soft lighting and a sense of mystery',
		image: '/assets/images/apps/ai-image-generator/moods/mystical.webp',
		color: '#19232e'
	},
	{
		value: 'tense',
		label: 'Tense',
		description: 'A tense and anxious scene with bold colors and dynamic lighting',
		image: '/assets/images/apps/ai-image-generator/moods/tense.webp',
		color: '#D32F2F'
	},
	{
		value: 'uplifting',
		label: 'Uplifting',
		description: 'An uplifting and inspiring scene with bold colors and dynamic lighting',
		image: '/assets/images/apps/ai-image-generator/moods/uplifting.webp',
		color: '#FFC107'
	}
];

export default moodPresets;
