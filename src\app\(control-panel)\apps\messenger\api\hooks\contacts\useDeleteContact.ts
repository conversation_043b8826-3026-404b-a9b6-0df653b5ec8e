import { useMutation, useQueryClient } from '@tanstack/react-query';
import { messengerApiService } from '../../services/messengerApiService';
import { contactQuery<PERSON>ey } from './useContact';
import { contactsQuery<PERSON>ey } from './useContacts';

export function useDeleteContact() {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: messengerApiService.contacts.delete,
		onSuccess: (_, id) => {
			queryClient.invalidateQueries({ queryKey: contactsQuery<PERSON>ey });
			queryClient.invalidateQueries({ queryKey: contactQuery<PERSON>ey(id) });
		}
	});
}
