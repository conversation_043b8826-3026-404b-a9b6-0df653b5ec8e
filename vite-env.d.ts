/// <reference types="vite/client" />

interface ImportMetaEnv {
	readonly VITE_MAP_KEY: string;
	readonly VITE_API_KEY: string;
	readonly VITE_AUTH_DOMAIN: string;
	readonly VITE_DATABASE_URL: string;
	readonly VITE_PROJECT_ID: string;
	readonly VITE_STORAGE_BUCKET: string;
	readonly VITE_MESSAGING_SENDER_ID: string;

	// MySQL Database Configuration
	readonly VITE_MYSQL_HOST: string;
	readonly VITE_MYSQL_PORT: string;
	readonly VITE_MYSQL_USER: string;
	readonly VITE_MYSQL_PASSWORD: string;
	readonly VITE_MYSQL_DATABASE: string;

	// Backend API Configuration
	readonly VITE_BACKEND_API_URL: string;
	readonly VITE_API_BASE_URL: string;

	// MSW Configuration
	readonly VITE_USE_MSW: string;
	readonly VITE_BYPASS_MSW_FOR_EXTERNAL: string;
}

interface ImportMeta {
	readonly env: ImportMetaEnv;
}
