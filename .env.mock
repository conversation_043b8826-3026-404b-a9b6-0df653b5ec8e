# Configuración para desarrollo con datos mock (MSW habilitado)
# Copia este archivo como .env.local para usar datos mock en desarrollo

# API Base URL
VITE_API_BASE_URL=http://localhost:3000

# MySQL Database Configuration (no se usa en modo mock)
VITE_MYSQL_HOST=**************
VITE_MYSQL_PORT=3306
VITE_MYSQL_USER=ncornejo
VITE_MYSQL_PASSWORD=N1c0l7as17#
VITE_MYSQL_DATABASE=renovation

# Backend API Configuration (no se usa en modo mock)
VITE_BACKEND_API_URL=http://**************:8080

# MSW Configuration - Habilitado para usar datos mock
VITE_USE_MSW=true
VITE_BYPASS_MSW_FOR_EXTERNAL=true

# Logging
VITE_ENABLE_LOGGING=true
VITE_LOG_LEVEL=debug

# Google Map Configurations
VITE_MAP_KEY=your_google_map_api_key

# Firebase Auth Configurations
VITE_FIREBASE_API_KEY=YOUR_API_KEY
VITE_FIREBASE_AUTH_DOMAIN=your-dev-app.firebaseapp.com
VITE_FIREBASE_DATABASE_URL=https://your-dev-app.firebaseio.com
VITE_FIREBASE_PROJECT_ID=your-dev-app
VITE_FIREBASE_STORAGE_BUCKET=your-dev-app.appspot.com
VITE_FIREBASE_MESSAGING_SENDER_ID=YOUR_MESSAGING_SENDER_ID
VITE_FIREBASE_APP_ID=YOUR_APP_ID

# AWS Cognito Configurations
VITE_AWS_PROJECT_REGION=
VITE_AWS_COGNITO_IDENTITY_POOL_ID=
VITE_AWS_COGNITO_REGION=
VITE_AWS_USER_POOLS_ID=
VITE_AWS_USER_POOLS_WEB_CLIENT_ID=
VITE_AWS_OAUTH_DOMAIN=
VITE_AWS_OAUTH_REDIRECT_SIGN_IN=http://localhost:3000
VITE_AWS_OAUTH_REDIRECT_SIGN_OUT=http://localhost:3000
